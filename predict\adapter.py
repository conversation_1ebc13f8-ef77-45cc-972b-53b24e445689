import os
from collections import defaultdict
import numpy as np
from dataprocess.volume import Volume

from predict.model_manager import SAM2ModelManager
from predict.predictor import FewshotPredictor
from predict.config_manager import ConfigManager

from hydra import initialize
import random
import torch


class FewshotAdapter:
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.predictor = FewshotPredictor(config_manager)
        self.sam_manager = SAM2ModelManager(self.predictor.work_device, config_manager)
        self.sam_manager.load_model()
        self.sam_predictor = self.sam_manager.get_predictor()
        self.working_direction = "z"
        self.condition_frames = defaultdict(dict)
        self.working_volume = None

        # 添加掩码提示存储结构

        self.slice_cache = {}  # {direction: (frames, mean, std)}

    def _switch_direction(self, volume: Volume, direction: str, **kwargs):
        """切换工作方向并加载体积切片"""
        # 检查缓存
        if direction in self.slice_cache:
            frames, volume_mean, volume_std = self.slice_cache[direction]
        else:
            # 生成新的切片
            volume.volume_to_frames(direction=direction, use_pil=False)
            frames = volume.frames

            volume_mean = kwargs.get("mean")
            volume_std = kwargs.get("std")

            # 缓存结果
            self.slice_cache[direction] = (frames, volume_mean, volume_std)

        # 加载到预测器
        self.sam_manager.cleanup()
        self.sam_predictor.load_volume(frames, volume_mean, volume_std)
        self.sam_predictor.set_init_state(images_on_cpu=True)
        self.working_direction = direction

    # For interactive prediction
    def load_volume_to_predictor(
        self, volume: Volume, direction: str = "z", **kwargs
    ) -> None:
        """
        将体积数据加载到预测器中

        Args:
            volume: 体积数据
            direction: 方向，默认为 "z"
            **kwargs: 其他参数（如 mean, std 等）
        """
        # 如果加载的是新的体积数据，则清空条件帧并修改 working_volume
        if not (
            self.working_volume is not None
            and volume.volume_path == self.working_volume.volume_path
        ):
            self.condition_frames = defaultdict(dict)
            self.working_volume = volume

        self.working_direction = direction
        volume.volume_to_frames(direction=direction, use_pil=False)
        frames = volume.frames
        volume_mean = kwargs.get("mean", None)
        volume_std = kwargs.get("std", None)
        self.sam_manager.cleanup()
        self.sam_predictor.load_volume(frames, volume_mean, volume_std)
        self.sam_predictor.set_init_state(images_on_cpu=True)

    def add_point_prompt(
        self, frame_index: int, obj_id: int, point: tuple, label: int, direction: str
    ) -> None:
        """
        添加点提示

        Args:
            frame_index: 帧索引
            obj_id: 对象ID
            point: 点坐标 (x, y)
            label: 标签，0 表示背景，1 表示前景
            direction: 方向 (x, y, z)
        """
        # 如果方向不匹配，切换到指定方向
        if direction != self.working_direction:
            if self.working_volume:
                self._switch_direction(self.working_volume, direction)
            else:
                raise ValueError("Volume not loaded before adding prompt")

        self.sam_predictor.add_point_prompt(frame_index, obj_id, point, label)

    def add_box_prompt(
        self, frame_index: int, obj_id: int, box: tuple, direction: str
    ) -> None:
        """
        添加框提示

        Args:
            frame_index: 帧索引
            obj_id: 对象ID
            box: 框坐标 (x1, y1, x2, y2)
            direction: 方向 (x, y, z)
        """
        # 如果方向不匹配，切换到指定方向
        if direction != self.working_direction:
            if self.working_volume:
                self._switch_direction(self.working_volume, direction)
            else:
                raise ValueError("Volume not loaded before adding prompt")

        self.sam_predictor.add_box_prompt(frame_index, obj_id, box)

    def add_mask_prompt(
        self, frame_index: int, obj_id: int, mask: np.ndarray, direction: str
    ) -> None:
        """
        添加掩码提示

        Args:
            frame_index: 帧索引
            obj_id: 对象ID
            mask: 掩码数据 (2D数组，前景为1，背景为0)
            direction: 方向 (x, y, z)
        """
        # 如果方向不匹配，切换到指定方向
        if direction != self.working_direction:
            if self.working_volume:
                self._switch_direction(self.working_volume, direction)
            else:
                raise ValueError("Volume not loaded before adding prompt")

        # 确保掩码是二值化的
        binary_mask = (mask > 0).astype(np.uint8)

        # 存储掩码提示

        self.condition_frames[direction][frame_index] = binary_mask



    def predict_single_frame(self, frame_index: int, direction: str) -> np.ndarray:
        """
        预测单帧

        Args:
            frame_index: 帧索引
            direction: 方向 (x, y, z)

        Returns:
            预测结果，前景值为对应的 obj_id，背景值为 0
        """
        # # 检查是否已存在条件掩码
        # if (
        #     direction in self.condition_frames
        #     and frame_index in self.condition_frames[direction]
        # ):
        #     return self.condition_frames[direction][frame_index]
        # 如果方向不匹配，切换到指定方向
        if direction != self.working_direction:
            if self.working_volume:
                self._switch_direction(self.working_volume, direction)
            else:
                raise ValueError("Volume not loaded before prediction")

        mask = self.sam_predictor.predict_single_frame(frame_index)

        self.condition_frames[direction][frame_index] = mask

        return mask

    def predict_all(self) -> np.ndarray:
        """
        预测所有帧

        Args:
            frame_index: 帧索引

        Returns:
            预测结果
        """
        self.sam_predictor.set_start_frame(0)
        self.sam_predictor.add_prompts_to_state()
        self.sam_predictor.predict_single(twice=False)
        mask = self.sam_predictor.get_id_masks()
        all_false = not mask.any()
        return mask.cpu().detach().numpy()

    def predict(self):
        self.sam_manager.cleanup()
        output_dir = self.config_manager.get("task.output_dir")
        os.makedirs(output_dir, exist_ok=True)
        self.predictor.to_sdf = False
        return self.predictor.predict(
            volume_path=self.working_volume,
            output_dir=output_dir,
            few_masks=self.condition_frames,
            save_results=False,
        )


def load_config(config_name="config", task_name="fewshot", overrides=None):
    """
    加载配置文件并指定配置组

    Args:
        config_name: 配置文件名称
        task_name: 任务名称/配置组
        overrides: 配置覆盖列表，例如 ["task.output_dir=/path/to/output"]

    Returns:
        配置管理器实例
    """
    from hydra import compose
    from omegaconf import OmegaConf

    cwd = os.getcwd()

    # 构建覆盖列表
    override_list = [f"+task={task_name}"]
    if overrides:
        override_list.extend(overrides)

    # 组合配置，添加task配置组和其他覆盖
    cfg = compose(config_name=config_name, overrides=override_list)

    # 手动替换 ${hydra:runtime.cwd}
    OmegaConf.to_container(cfg, resolve=False)
    cfg_str = str(cfg)
    cfg_str = cfg_str.replace("${hydra:runtime.cwd}", cwd)
    cfg = OmegaConf.create(cfg_str)

    OmegaConf.resolve(cfg)
    return ConfigManager.from_hydra_config(cfg)


def train_unet(config_name="config", task_name="trainu", overrides=None):
    """
    训练UNet模型的接口方法，用于GUI调用

    Args:
        config_name: 配置文件名称，默认为 "config"
        task_name: 任务名称/配置组，默认为 "train_unet"
        overrides: 配置覆盖列表，例如 ["task.output_dir=/path/to/output"]

    Returns:
        训练结果信息字典
    """
    from pytorch3dunet.unet3d.config import load_config_using_hydra
    from pytorch3dunet.unet3d.trainer import create_trainer
    from pytorch3dunet.unet3d.utils import get_logger
    from omegaconf import OmegaConf
    from hydra import initialize
    from hydra.core.global_hydra import GlobalHydra

    logger = get_logger("UNetTraining")

    try:
        # 确保Hydra已初始化（在multiprocessing子进程中可能需要重新初始化）
        if not GlobalHydra().is_initialized():
            initialize(config_path="../config", version_base=None)

        # 加载主配置
        config_manager = load_config(config_name, task_name, overrides)

        # 获取UNet配置路径和覆盖参数
        unet_config_path = config_manager.get("task.unet_config_path")
        unet_config_overrides = config_manager.get("task.override_unet_config")

        if not unet_config_path:
            raise ValueError("UNet config path not specified in task configuration")

        # 加载UNet配置
        unet_cfg = load_config_using_hydra(unet_config_path, unet_config_overrides)

        # 打印并记录配置
        logger.info("UNet Training Configuration:")
        logger.info(OmegaConf.to_yaml(unet_cfg))

        # 设置随机种子
        manual_seed = unet_cfg.get("manual_seed", None)
        if manual_seed is not None:
            logger.info(f"Setting random seed to {manual_seed}")
            logger.warning(
                "Using CuDNN deterministic setting. This may slow down the training!"
            )
            random.seed(manual_seed)
            torch.manual_seed(manual_seed)
            torch.backends.cudnn.deterministic = True

        # 创建训练器并启动训练
        logger.info("Creating trainer...")
        trainer = create_trainer(unet_cfg)

        logger.info("Starting training...")
        trainer.fit()

        logger.info("Training completed successfully!")

        return {
            "status": "success",
            "message": "UNet training completed successfully",
            "checkpoint_dir": unet_cfg.get("trainer", {}).get("checkpoint_dir", ""),
            "config_path": unet_config_path,
        }

    except Exception as e:
        error_msg = f"UNet training failed: {str(e)}"
        logger.error(error_msg)
        return {"status": "error", "message": error_msg, "error": str(e)}


def main():
    import sys
    import json

    # 训练模式
    task_name = "trainu"

    # 主动初始化 hydra
    initialize(config_path="../config", version_base=None)

    # 执行训练
    result = train_unet(config_name="config", task_name=task_name)

    # 输出结果
    print(json.dumps(result, indent=2))

    # 根据训练结果设置退出码
    if result["status"] == "error":
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
