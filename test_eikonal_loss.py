#!/usr/bin/env python3
"""
Test script for the modified MSEWithEikonalLoss function.
Tests that truncated regions are properly excluded from Eikonal loss computation.
"""

import torch
import numpy as np
import sys
import os

# Add the UNet module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'UNet', 'pytorch3dunet'))

from unet3d.losses import MSEWithEikonalLoss


def create_test_sdf(shape, truncation_value=5.0):
    """
    Create a test SDF with known truncated regions.
    
    Args:
        shape: Shape of the SDF volume (B, C, D, H, W)
        truncation_value: Maximum absolute value for truncation
        
    Returns:
        torch.Tensor: Test SDF with truncated regions
    """
    # Create a simple sphere SDF
    B, C, D, H, W = shape
    sdf = torch.zeros(shape, dtype=torch.float32)
    
    # Create coordinates
    z, y, x = torch.meshgrid(
        torch.linspace(-1, 1, D),
        torch.linspace(-1, 1, H), 
        torch.linspace(-1, 1, W),
        indexing='ij'
    )
    
    # Distance from center
    distance = torch.sqrt(x**2 + y**2 + z**2)
    
    # Create SDF (negative inside sphere, positive outside)
    sphere_sdf = distance - 0.5
    
    # Apply truncation
    sphere_sdf = torch.clamp(sphere_sdf, -truncation_value, truncation_value)
    
    # Expand to batch and channel dimensions
    for b in range(B):
        for c in range(C):
            sdf[b, c] = sphere_sdf
    
    return sdf


def test_eikonal_loss_with_truncation():
    """Test that Eikonal loss properly handles truncated regions."""
    print("Testing MSEWithEikonalLoss with truncation handling...")
    
    # Create test data
    shape = (2, 1, 32, 32, 32)  # Small volume for testing
    truncation_value = 3.0
    
    # Create target SDF with truncated regions
    target_sdf = create_test_sdf(shape, truncation_value)
    
    # Create predicted SDF (slightly different from target)
    pred_sdf = target_sdf + 0.1 * torch.randn_like(target_sdf)
    
    # Test with different truncation thresholds
    thresholds = [0.8, 0.9, 0.95, 0.99]
    
    for threshold in thresholds:
        print(f"\nTesting with truncation_threshold={threshold}")
        
        # Create loss function
        loss_fn = MSEWithEikonalLoss(
            lambda_eikonal=0.1,
            voxel_size=(1.0, 1.0, 1.0),
            truncation_threshold=threshold
        )
        
        # Compute loss
        total_loss = loss_fn(pred_sdf, target_sdf)
        
        # Check mask creation
        mask = loss_fn.create_non_truncated_mask(target_sdf)
        num_valid_voxels = torch.sum(mask).item()
        total_voxels = mask.numel()
        
        print(f"  Total loss: {total_loss.item():.6f}")
        print(f"  Valid voxels: {num_valid_voxels}/{total_voxels} ({100*num_valid_voxels/total_voxels:.1f}%)")
        
        # Verify that higher thresholds include more voxels
        if threshold > 0.8:
            assert num_valid_voxels > 0, f"No valid voxels found with threshold {threshold}"
    
    print("\n✓ All tests passed!")


def test_gradient_computation():
    """Test gradient computation in non-truncated regions."""
    print("\nTesting gradient computation...")
    
    # Create a simple test case with known gradients
    shape = (1, 1, 16, 16, 16)
    
    # Create a linear SDF (gradient should be constant)
    sdf = torch.zeros(shape, dtype=torch.float32)
    z, y, x = torch.meshgrid(
        torch.linspace(-1, 1, 16),
        torch.linspace(-1, 1, 16),
        torch.linspace(-1, 1, 16),
        indexing='ij'
    )
    
    # Linear function: f(x,y,z) = x + y + z
    # Gradient should be (1, 1, 1) everywhere
    sdf[0, 0] = x + y + z
    
    # Create loss function
    loss_fn = MSEWithEikonalLoss(lambda_eikonal=1.0, truncation_threshold=0.99)
    
    # Compute gradients
    gradients = loss_fn.compute_normalized_gradients(sdf)
    
    # Check gradient magnitude (should be close to sqrt(3) ≈ 1.732)
    gradient_magnitude = torch.norm(gradients, p=2, dim=-1)
    expected_magnitude = np.sqrt(3.0)
    
    print(f"  Expected gradient magnitude: {expected_magnitude:.3f}")
    print(f"  Computed gradient magnitude (mean): {gradient_magnitude.mean().item():.3f}")
    print(f"  Computed gradient magnitude (std): {gradient_magnitude.std().item():.3f}")
    
    # Check that gradients are approximately (1, 1, 1) normalized by voxel size
    mean_gradient = gradients.mean(dim=(0, 1, 2, 3, 4))
    print(f"  Mean gradient components: [{mean_gradient[0]:.3f}, {mean_gradient[1]:.3f}, {mean_gradient[2]:.3f}]")
    
    print("✓ Gradient computation test passed!")


def test_edge_cases():
    """Test edge cases like all-truncated or no-truncated regions."""
    print("\nTesting edge cases...")
    
    shape = (1, 1, 8, 8, 8)
    
    # Case 1: All values are truncated
    all_truncated = torch.full(shape, 5.0, dtype=torch.float32)  # All at max value
    pred = all_truncated + 0.01 * torch.randn_like(all_truncated)
    
    loss_fn = MSEWithEikonalLoss(lambda_eikonal=0.1, truncation_threshold=0.9)
    loss = loss_fn(pred, all_truncated)
    
    print(f"  All-truncated case loss: {loss.item():.6f}")
    
    # Case 2: No values are truncated
    no_truncated = torch.randn(shape) * 0.1  # Small values, none truncated
    pred = no_truncated + 0.01 * torch.randn_like(no_truncated)
    
    loss = loss_fn(pred, no_truncated)
    print(f"  No-truncated case loss: {loss.item():.6f}")
    
    print("✓ Edge cases test passed!")


if __name__ == "__main__":
    print("Testing modified MSEWithEikonalLoss function...")
    print("=" * 50)
    
    test_eikonal_loss_with_truncation()
    test_gradient_computation()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("All tests completed successfully!")
