unet_config_path: "unet_config/train_config_sdf.yaml"

override_unet_config:
  trainer:
    checkpoint_dir: "${output_root}/fs_unet_ckpt/uro_mito"
  loaders:
    train_only: true
    resolution: 512
    config:
      train:
        root_dir: "${datasets_root}/main"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: seg
        normalization_file: "${datasets_root}/main/normalization_params.json"
        datasets_info:
          - name: urocell
            organelles:
              - em: fv_lyso_mito
                seg: mito
              # - em: fv_lyso_mito
              #   seg: fv
              # - em: fv_lyso_mito
              #   seg: lyso
              # - em: golgi
              #   seg: golgi
