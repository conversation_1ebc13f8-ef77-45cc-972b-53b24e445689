unet_config_path: "unet_config/train_config_sdf.yaml"

override_unet_config:
  trainer:
    checkpoint_dir: "${output_root}/unet_sdf_ckpt/nuc_m"
  loaders:
    resolution: 512
    config:
      train:
        root_dir: "${datasets_root}/main"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: seg
        normalization_file: "${datasets_root}/main/normalization_params.json"
        datasets_info:
          - name: jrc_mus-liver
            organelles:
              - em: nuc
                seg: nuc
      val:
        root_dir: "${datasets_root}/main"
        normalization_file: "${datasets_root}/main/normalization_params.json"
        mask_dir_name: sdf_avg
        ref_mask_dir_name: seg
        datasets_info:
          - name: jrc_mus-liver
            organelles:
              - em: nuc
                seg: nuc
