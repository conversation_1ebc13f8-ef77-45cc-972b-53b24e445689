# Eikonal Loss Modification for Truncated SDF

## Problem Description

The original Eikonal loss implementation computed the gradient magnitude constraint (||∇φ|| = 1) across the entire SDF volume. However, in truncated SDFs, regions where the distance field reaches its maximum (positive) or minimum (negative) values are artificially clamped, meaning their gradients do not naturally satisfy the Eikonal equation. Including these regions in the loss computation can lead to incorrect gradient penalties.

## Solution

The modified `MSEWithEikonalLoss` class now includes truncation-aware masking that excludes truncated regions from the Eikonal loss computation while still including them in the MSE loss.

### Key Changes

1. **Added `truncation_threshold` parameter**: Controls the threshold for identifying truncated regions
2. **Implemented `create_non_truncated_mask()`**: Creates a boolean mask to identify valid (non-truncated) regions
3. **Implemented `compute_masked_eikonal_loss()`**: Computes Eikonal loss only in valid regions
4. **Updated configuration support**: Added support for the new parameter in the loss factory function

### New Parameters

- `truncation_threshold` (float, default=0.95): Regions with |SDF| > threshold × max(|SDF|) are considered truncated
- `voxel_size` (tuple, default=(1.0, 1.0, 1.0)): Physical voxel dimensions for gradient scaling
- `lambda_eikonal` (float, default=0.1): Weight for the Eikonal regularization term

## Usage

### In Configuration File

```yaml
loss:
  name: MSEWithEikonal
  lambda_eikonal: 0.1
  voxel_size: [1.0, 1.0, 1.0]
  truncation_threshold: 0.95
```

### In Python Code

```python
from unet3d.losses import MSEWithEikonalLoss

# Create loss function with truncation handling
loss_fn = MSEWithEikonalLoss(
    lambda_eikonal=0.1,
    voxel_size=(1.0, 1.0, 1.0),
    truncation_threshold=0.95
)

# Use in training
loss = loss_fn(predicted_sdf, target_sdf)
```

## Parameter Tuning Guidelines

### `truncation_threshold`
- **0.8-0.9**: Aggressive truncation detection, excludes more regions
- **0.95**: Recommended default, good balance
- **0.98-0.99**: Conservative detection, includes more regions

### `lambda_eikonal`
- **0.05-0.1**: Standard weight for most applications
- **0.1-0.2**: Higher weight when many regions are truncated
- **0.01-0.05**: Lower weight for high-resolution data with fine details

### `voxel_size`
- Set to actual physical voxel dimensions
- Important for anisotropic data (e.g., medical imaging)
- Affects gradient magnitude computation

## Benefits

1. **Improved Training Stability**: Excludes problematic truncated regions from gradient penalties
2. **Better Convergence**: Focuses Eikonal constraint on meaningful regions
3. **Configurable**: Adjustable threshold allows fine-tuning for different datasets
4. **Backward Compatible**: Default parameters maintain similar behavior to original implementation

## Testing

The modification has been tested with:
- Various truncation thresholds (0.8-0.99)
- Different SDF shapes and truncation patterns
- Edge cases (all-truncated, no-truncated regions)
- Gradient computation accuracy

All tests pass successfully, confirming the implementation works as expected.

## Files Modified

- `UNet/pytorch3dunet/unet3d/losses.py`: Main implementation
- `test_eikonal_loss.py`: Test script
- `example_eikonal_config.yaml`: Configuration example
