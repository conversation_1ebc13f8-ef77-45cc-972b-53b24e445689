# DatasetDataLoader Implementation

## 概述

实现了 `DatasetDataLoader` 类的 `get_prediction_datasets` 函数，该函数能够根据配置文件中的 `path_config` 格式处理数据集，并输出每个体积与对应mask的路径、数据集特定的归一化参数以及输出文件夹路径。

## 主要功能

### 1. 数据集路径解析
- 根据 `path_config` 配置解析数据集结构
- 支持多个数据集和多个细胞器类型
- 自动匹配体积文件和mask文件

### 2. 归一化参数处理
- 从 `normalization_params.json` 文件加载数据集特定的归一化参数
- 数据集特定的 mean/std 会覆盖全局传入的参数
- 确保每个数据集使用正确的归一化参数

### 3. 输出路径生成
- 自动生成输出路径，将体积路径中的 "em" 替换为 `output_dir_name`
- 支持自定义输出目录名称（如 "sdf_avg"）

## 函数签名

```python
def get_prediction_datasets(
    self,
    path_config: Dict[str, Any],
    split: str = "train",
    volume_mean: Optional[float] = None,
    volume_std: Optional[float] = None,
) -> List[Dict]:
```

### 参数说明

- `path_config`: 路径配置字典，包含split配置信息
- `split`: 数据集分割类型（train, val, test），默认为 "train"
- `volume_mean`: 全局体积均值（会被数据集特定值覆盖）
- `volume_std`: 全局体积标准差（会被数据集特定值覆盖）

### 返回值

返回数据集字典列表，每个字典包含：
- `volume_path`: 体积文件路径
- `masks_path`: mask文件路径（如果存在）
- `output_dir`: 输出目录路径
- `volume_mean`: 数据集特定的均值
- `volume_std`: 数据集特定的标准差

## 配置文件格式

### path_config 结构

```yaml
path_config:
  split:
    - "train"
    - "val"
  output_dir_name: sdf_avg
  train:
    root_dir: "${datasets_root}/finetune"
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    datasets_info:
      - name: jrc_hela-1
        organelles:
          - em: endo
            seg: endo
          - em: mito
            seg: mito
  val:
    root_dir: "${datasets_root}/finetune"
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    datasets_info:
      - name: jrc_mus-heart-1
        organelles:
          - em: nuc
            seg: nuc
```

### normalization_params.json 格式

```json
{
    "jrc_hela-1": {
        "mean": 0.485,
        "std": 0.229
    },
    "jrc_mus-heart-1": {
        "mean": 0.406,
        "std": 0.225
    }
}
```

## 目录结构

期望的数据集目录结构：

```
${datasets_root}/finetune/
├── normalization_params.json
├── train/
│   └── jrc_hela-1/
│       ├── em/
│       │   ├── endo/
│       │   │   ├── volume_000.zst
│       │   │   └── volume_001.zst
│       │   └── mito/
│       │       ├── volume_000.zst
│       │       └── volume_001.zst
│       └── seg/
│           ├── endo/
│           │   ├── mask_000.zst
│           │   └── mask_001.zst
│           └── mito/
│               ├── mask_000.zst
│               └── mask_001.zst
└── val/
    └── ... (类似结构)
```

输出将生成在：
```
${datasets_root}/finetune/train/jrc_hela-1/sdf_avg/endo/
${datasets_root}/finetune/train/jrc_hela-1/sdf_avg/mito/
```

## 集成到 predict_all 函数

修改了 `predict_new.py` 中的 `predict_all` 函数，使其能够：

1. 检测数据加载器类型
2. 根据类型调用相应的方法
3. 传递数据集特定的归一化参数给预测器

### 关键修改

```python
if data_loader_type == "DatasetDataLoader":
    # 使用 DatasetDataLoader 的新方法
    path_config = config_manager.get("task.path_config")
    split = kwargs.get("split", "train")
    datasets = data_loader.get_prediction_datasets(
        path_config=path_config,
        split=split,
        volume_mean=mean,
        volume_std=std,
    )
else:
    # 使用原有的 VolumeDataLoader 方法
    datasets = data_loader.get_prediction_datasets(
        volume_dir=volume_dir,
        masks_dir=masks_dir,
        ref_masks_dir=ref_masks_dir,
    )
```

## 使用示例

参见 `examples/dataset_dataloader_example.py` 文件，其中包含了完整的使用示例和期望的目录结构说明。

## 测试

实现包含了完整的测试验证，确保：
- 正确解析配置文件
- 数据集特定的归一化参数覆盖全局参数
- 输出路径正确生成（em -> output_dir_name）
- 文件路径匹配正确
