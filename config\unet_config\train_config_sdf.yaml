# Sample configuration file for training a 3D U-Net on a task of predicting the boundaries in 3D stack of the Arabidopsis
# ovules acquired with the confocal microscope. Training done with a combination of Binary Cross-Entropy and DiceLoss.
# Download train data from: https://osf.io/x9yns/
# Download validation data from: https://osf.io/xp5uf/
# Download test data from: https://osf.io/8jz7e/
device: "cuda"  # 或 "cpu"（若无需GPU）
model:
  name: sdfUNet3D

  # number of input channels to the model
  in_channels: 1
  # number of output channels
  out_channels: 1
  # determines the order of operators in a single layer (gcr - GroupNorm+Conv3d+ReLU)
  layer_order: gcr
  # number of features at each level of the U-Net
  f_maps: [32, 64, 128, 256]
  # number of groups in the groupnorm
  num_groups: 8
  # apply element-wise nn.Sigmoid after the final 1x1x1 convolution, otherwise apply nn.Softmax
  final_sigmoid: true
  is_segmentation: false
# loss function to be used during training
loss:
  # name: BCEDiceLoss
  # # a target value that is ignored and does not contribute to the input gradient
  # ignore_index: null
  # # skip the last channel in the target (i.e. when last channel contains data not relevant for the loss)
  # skip_last_target: true
  name: "MSEWithEikonal"
  lambda_eikonal: 0.5  # 可调节的权重参数
optimizer:
  # initial learning rate
  learning_rate: 0.0002
  # weight decay
  weight_decay: 0.00001
# evaluation metric
eval_metric:
  # use AdaptedRandError metric
  name: MSE
lr_scheduler:
  name: ReduceLROnPlateau
  # make sure to use the 'min' mode cause lower AdaptedRandError is better
  mode: min
  factor: 0.2
  # number of *validation runs* with no improvement after which learning rate will be reduced
  patience: 8
trainer:
  # model with lower eval score is considered better
  eval_score_higher_is_better: False
  # path to the checkpoint directory
  checkpoint_dir: /data2/wyx/3d_seg/output/unet_ckpt
  # path to latest checkpoint; if provided the training will be resumed from that checkpoint
  resume: null
  # path to the best_checkpoint.pytorch; to be used for fine-tuning the model with additional ground truth
  pre_trained: null
  skip_train_validation: False
  # how many iterations between validations
  validate_after_iters: 2000
  # how many iterations between tensorboard logging
  log_after_iters: 50
  # max number of epochs
  max_num_epochs: 50
  # max number of iterations
  max_num_iterations: 150000
# Configure training and validation loaders
loaders:
  resolution: null
  train_only: false
  using_sdf: true
  config: ???
  dataset: StandardZstdDataset
  # how many subprocesses to use for data loading
  num_workers: 8
  # path to the raw data within the H5
  raw_internal_path: raw
  # path to  the label data within the H5
  label_internal_path: label
  # apply random shifting and scaling of the patches; value of 20 mean that patches may shrink/stretch by 20px in each dimension
  random_scale: 20
  # random scale execution probability; since random scale is quite slow for 3D data, we set it to 0.1
  random_scale_probability: 0.1
  # configuration of the train loader
  train:
    # path to the training datasets
    raw_file_paths:
      - /data2/wyx/3d_seg/finetune_ready/em/single
    label_file_paths:
      - /data2/wyx/3d_seg/finetune_ready/seg/single_sdf

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      stride_shape: [80, 80, 80]
      # minimum volume of the labels in the patch
      threshold: 0.6
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    transformer:
      raw:
        - name: RandomBrightness
        - name: Standardize
        # randomly flips the volume in one of the axis
        - name: RandomFlip
        # randomly rotates the volume with 90 deg across a randomly chosen plane
        - name: RandomRotate90
        - name: ToTensor
          expand_dims: true
      label:
        - name: RandomFlip
        - name: RandomRotate90
        - name: ToTensor
          expand_dims: true

  # configuration of the val loader
  val:
    # path to the val datasets
    raw_file_paths:
      - /data2/wyx/3d_seg/finetune_ready/em/single
    label_file_paths:
      - /data2/wyx/3d_seg/finetune_ready/seg/single_sdf

    # SliceBuilder configuration, i.e. how to iterate over the input volume patch-by-patch
    slice_builder:
      name: FilterSliceBuilder
      # train patch size given to the network (adapt to fit in your GPU mem, generally the bigger patch the better)
      patch_shape: [128, 128, 128]
      # train stride between patches
      # stride_shape: [128, 128, 128]
            # train stride between patches
      stride_shape: [128, 128, 128]
      # minimum volume of the labels in the patch
      threshold: 0.6
      # probability of accepting patches which do not fulfil the threshold criterion
      slack_acceptance: 0.01

    # data augmentation
    transformer:
      raw:
        - name: Standardize
        - name: ToTensor
          expand_dims: true
      label:
        - name: ToTensor
          expand_dims: true